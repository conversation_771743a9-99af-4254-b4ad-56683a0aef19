"use client";
import React, { useState } from 'react'
import Image from 'next/image'
import {Button} from '@/components/ui/button'
import { Select, SelectTrigger, SelectContent, SelectItem } from '@/components/ui/select'
import CreateShipmentModal from '@/components/modals/CreateShipmentModal'

const Dashboard = () => {
  const [isCreateShipmentModalOpen, setIsCreateShipmentModalOpen] = useState(false);

  return (
    <>
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h1 className="text-[24px] font-semibold text-woodsmoke-950 mb-2">
          Welcome Back 👋
        </h1>
        <p className="text-woodsmoke-600 text-tiny font-normal text-[12px]">
          What would you like to do today?
        </p>
      </div>

      {/* Action Cards */}
      <div className="flex flex-col lg:flex-row w-full gap-6 lg:gap-10">
        {/* Orange Card - Start a new shipment */}
        <div
          className="relative overflow-hidden w-full lg:flex-1"
          style={{
            height: '224px',
            borderRadius: '20px',
            background: 'linear-gradient(329.37deg, #FFE6D2 -10.23%, #F9A967 36.1%, #F57D1C 71.29%)',
            opacity: 1
          }}
        >
          <div className="p-6 h-full flex flex-col justify-between">
            <div>
              <h3 className="text-white text-xl font-semibold mb-2">
                Start a new shipment
              </h3>
              <p className="text-white/90 text-sm">
                This is where your body text comes in
              </p>
            </div>

            <Button
              onClick={() => setIsCreateShipmentModalOpen(true)}
              className="!btn-primary backdrop-blur-sm text-white px-4 py-2 rounded-8 font-medium hover:[#F57D1C] transition-colors w-fit"
            >
              Create your shipment →
            </Button>
          </div>

          {/* Illustration */}
          <div
            className="absolute right-4 lg:right-8"
            style={{
              width: '176px',
              height: '176px',
              opacity: 1,
              top: '40px'
            }}
          >
            <Image
              src="/images/package.png"
              alt="Shipment illustration"
              width={176}
              height={176}
              className="object-contain"
            />
          </div>
        </div>

        {/* Blue Card - Track active shipment */}
        <div
          className="relative overflow-hidden w-full lg:flex-1"
          style={{
            height: '224px',
            borderRadius: '20px',
            background: 'linear-gradient(329.37deg, #E5F1FF -10.23%, #0A69E3 71.29%)',
            opacity: 1
          }}
        >
          <div className="p-6 h-full flex flex-col justify-between">
            <div>
              <h3 className="text-white text-xl font-semibold mb-2">
                Track active shipment
              </h3>
              <p className="text-white/90 text-sm">
                This is where your body text comes in
              </p>
            </div>

            <Button className="!bg-Blue-50 backdrop-blur-sm text-white px-4 py-2 rounded-lg font-medium hover:bg-white/30 transition-colors w-fit">
              Track shipment →
            </Button>
          </div>

          {/* Illustration */}
          <div
            className="absolute right-4 lg:right-8"
            style={{
              width: '208px',
              height: '208px',
              opacity: 1,
              top: '26px'
            }}
          >
            <Image
              src="/images/tracking.png"
              alt="Tracking illustration"
              width={208}
              height={208}
              className="object-contain"
            />
          </div>
        </div>
      </div>

      {/* Shipment History Section */}
      <div className="bg-[#FFFFFF] rounded-lg w-[full] h-[429px]  p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold text-[#454545]">
            Shipment History
          </h2>
          <div className="flex gap-4">
            <Select> 
              <SelectTrigger className="text-Woodsmoke-800 text-[12px]text-tiny font-regular border-woodsmoke-100 rounded-[8px]">
              This month
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="this-month">Last month</SelectItem>
                <SelectItem value="last-month">Last year</SelectItem>
              </SelectContent>
            </Select>

             <Select> 
              <SelectTrigger className="text-Woodsmoke-800 text-[12px]text-tiny font-regular border-woodsmoke-100 rounded-[8px]">
              All Status
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="this-month">Pending</SelectItem>
                <SelectItem value="last-month">Delivered</SelectItem>
                <SelectItem value="last-month">In Transit</SelectItem>
                <SelectItem value="last-month">Cancelled</SelectItem>
              </SelectContent>
            </Select>
             

            <button className="border border-primary-500 rounded-[8px] text-primary-500 px-[10px] py-[8px] text-[12px] text-regular text-tiny">
              Export ↓
            </button>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center w-full h-[328px] space-y-4">
          <Image
            src="/images/gift.png"
            alt="No shipments illustration"
            width={80}
            height={80}
            className="object-contain"
          />
          <div className="text-center">
            <p className="text-large font-semibold text-woodsmoke-950 text-[20px] mb-2">No Shipment Found</p>
            <p className="text-tiny font-regular text-Woodsmoke-600 text-[12px] ">
              You have not created any shipment <br /> request on this platform.
            </p>
          </div>
        </div>

      </div>

    </div>

    {/* Create Shipment Modal */}
    <CreateShipmentModal
      isOpen={isCreateShipmentModalOpen}
      onClose={() => setIsCreateShipmentModalOpen(false)}
    />
    </>
  )
}

export default Dashboard